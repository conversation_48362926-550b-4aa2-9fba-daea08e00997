import { Controller } from "@hotwired/stimulus";

// Holographic Card Controller
// Handles mouse tracking and tilt effects for holographic cards
export default class extends Controller {
  static targets = ["card"];
  
  static values = {
    intensity: { type: Number, default: 15 },
    maxTilt: { type: Number, default: 10 },
    perspective: { type: Number, default: 1000 },
    scale: { type: Number, default: 1.02 },
    speed: { type: Number, default: 300 }
  };

  connect() {
    this.isHovered = false;
    this.mousePosition = { x: 0, y: 0 };
    
    // Check for reduced motion preference
    this.prefersReducedMotion = window.matchMedia(
      "(prefers-reduced-motion: reduce)"
    ).matches;
    
    // Bind event handlers
    this.boundHandleMouseMove = this.handleMouseMove.bind(this);
    this.boundHandleMouseEnter = this.handleMouseEnter.bind(this);
    this.boundHandleMouseLeave = this.handleMouseLeave.bind(this);
    
    this.setupEventListeners();
  }

  disconnect() {
    this.removeEventListeners();
  }

  setupEventListeners() {
    if (this.hasCardTarget && !this.prefersReducedMotion) {
      this.cardTarget.addEventListener("mousemove", this.boundHandleMouseMove);
      this.cardTarget.addEventListener("mouseenter", this.boundHandleMouseEnter);
      this.cardTarget.addEventListener("mouseleave", this.boundHandleMouseLeave);
    }
  }

  removeEventListeners() {
    if (this.hasCardTarget) {
      this.cardTarget.removeEventListener("mousemove", this.boundHandleMouseMove);
      this.cardTarget.removeEventListener("mouseenter", this.boundHandleMouseEnter);
      this.cardTarget.removeEventListener("mouseleave", this.boundHandleMouseLeave);
    }
  }

  handleMouseMove(event) {
    if (this.prefersReducedMotion || !this.isHovered) return;

    const rect = this.cardTarget.getBoundingClientRect();
    const centerX = rect.left + rect.width / 2;
    const centerY = rect.top + rect.height / 2;
    
    // Calculate mouse position relative to card center
    const mouseX = event.clientX - centerX;
    const mouseY = event.clientY - centerY;
    
    // Normalize to -1 to 1 range
    const rotateX = (mouseY / (rect.height / 2)) * this.maxTiltValue;
    const rotateY = (mouseX / (rect.width / 2)) * this.maxTiltValue;
    
    // Apply transform with perspective
    this.applyTransform(-rotateX, rotateY);
    
    // Update CSS custom properties for gradient effects
    this.updateGradientPosition(event, rect);
  }

  handleMouseEnter(event) {
    if (this.prefersReducedMotion) return;
    
    this.isHovered = true;
    this.cardTarget.style.transition = `transform ${this.speedValue}ms ease-out`;
  }

  handleMouseLeave(event) {
    if (this.prefersReducedMotion) return;
    
    this.isHovered = false;
    
    // Reset transform
    this.cardTarget.style.transform = `
      perspective(${this.perspectiveValue}px) 
      rotateX(0deg) 
      rotateY(0deg) 
      scale(1)
    `;
    
    this.cardTarget.style.transition = `transform ${this.speedValue * 1.5}ms ease-out`;
    
    // Reset gradient position
    this.resetGradientPosition();
  }

  applyTransform(rotateX, rotateY) {
    const scale = this.isHovered ? this.scaleValue : 1;
    
    this.cardTarget.style.transform = `
      perspective(${this.perspectiveValue}px) 
      rotateX(${rotateX}deg) 
      rotateY(${rotateY}deg) 
      scale(${scale})
    `;
  }

  updateGradientPosition(event, rect) {
    // Calculate mouse position as percentage
    const x = ((event.clientX - rect.left) / rect.width) * 100;
    const y = ((event.clientY - rect.top) / rect.height) * 100;
    
    // Update CSS custom properties for dynamic gradients
    this.cardTarget.style.setProperty('--mouse-x', `${x}%`);
    this.cardTarget.style.setProperty('--mouse-y', `${y}%`);
    
    // Update shine effect position
    const shineElements = this.cardTarget.querySelectorAll('.holographic-shine');
    shineElements.forEach(shine => {
      shine.style.background = `
        linear-gradient(${x + y}deg, 
          rgba(255, 255, 255, 0.3) 0%, 
          transparent 20%, 
          transparent 80%, 
          rgba(255, 255, 255, 0.3) 100%)
      `;
    });
  }

  resetGradientPosition() {
    this.cardTarget.style.setProperty('--mouse-x', '50%');
    this.cardTarget.style.setProperty('--mouse-y', '50%');
    
    // Reset shine effect
    const shineElements = this.cardTarget.querySelectorAll('.holographic-shine');
    shineElements.forEach(shine => {
      shine.style.background = `
        linear-gradient(135deg, 
          rgba(255, 255, 255, 0.3) 0%, 
          transparent 20%, 
          transparent 80%, 
          rgba(255, 255, 255, 0.3) 100%)
      `;
    });
  }

  // Action methods that can be called from templates
  
  // Enable enhanced effects
  enableEnhancedEffects() {
    if (this.prefersReducedMotion) return;
    
    this.intensityValue = 20;
    this.maxTiltValue = 15;
    this.scaleValue = 1.05;
  }

  // Disable effects (for accessibility)
  disableEffects() {
    this.removeEventListeners();
    this.cardTarget.style.transform = 'none';
    this.resetGradientPosition();
  }

  // Trigger a subtle animation effect
  triggerPulse() {
    if (this.prefersReducedMotion) return;
    
    this.cardTarget.style.transition = 'transform 0.2s ease-out';
    this.cardTarget.style.transform = `scale(${this.scaleValue})`;
    
    setTimeout(() => {
      this.cardTarget.style.transform = 'scale(1)';
    }, 200);
  }
}
