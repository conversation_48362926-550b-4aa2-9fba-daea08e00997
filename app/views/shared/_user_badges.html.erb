<%#
  User Badges Display Component
  
  Usage:
  render 'shared/user_badges', user: user
  render 'shared/user_badges', user: user, context: 'profile_header'
  render 'shared/user_badges', user: user, context: 'card', limit: 2
  render 'shared/user_badges', user: user, context: 'gallery', limit: 2, icon_only: true
  
  Parameters:
  - user: User object (required) - must have active_badges method
  - context: string - 'profile_header', 'profile_section', 'card', 'gallery', 'compact' (default: 'card')
  - limit: integer - maximum number of badges to show (default: nil for no limit)
  - icon_only: boolean - show only icons for very compact display (default: false)
  - show_count: boolean - show +N indicator when badges exceed limit (default: true)
  - class_name: additional CSS classes for the container
%>

<%
  # Set default values
  context ||= 'card'
  limit ||= nil
  icon_only ||= false
  show_count = show_count.nil? ? true : show_count
  class_name ||= ''
  
  # Get active badges for the user
  active_badges = user.respond_to?(:active_badges) ? user.active_badges : []
  
  # Apply limit if specified
  displayed_badges = limit ? active_badges.limit(limit) : active_badges
  remaining_count = limit && active_badges.count > limit ? active_badges.count - limit : 0
  
  # Context-specific configurations
  case context
  when 'profile_header'
    badge_size = 'lg'
    badge_component = 'shared/badge'
    container_classes = 'flex flex-wrap gap-2 items-start'
    badge_classes = 'transform transition-all duration-300 hover:scale-110 hover:shadow-lg'
    holographic_intensity = 0.8
    rotation_factor = 15
    glow_intensity = 1.2
    count_classes = 'flex items-center justify-center w-12 h-8 text-xs font-medium text-stone-600 bg-stone-100 rounded-lg border border-stone-200'
    
  when 'profile_section'
    badge_size = 'md'
    badge_component = 'shared/badge'
    container_classes = 'flex flex-wrap gap-3'
    badge_classes = 'transform transition-transform duration-200 hover:scale-105'
    holographic_intensity = 0.6
    rotation_factor = 10
    glow_intensity = 0.9
    count_classes = 'flex items-center justify-center px-2 py-1 text-xs font-medium text-stone-600 bg-stone-100 rounded-md border border-stone-200'
    
  when 'card'
    badge_size = 'sm'
    badge_component = 'shared/badge_compact'
    container_classes = 'flex flex-wrap grid grid-cols-2 gap-1'
    badge_classes = 'transform transition-transform duration-200 hover:scale-105'
    holographic_intensity = 0.4
    rotation_factor = 8
    glow_intensity = 0.6
    count_classes = 'flex items-center justify-center px-2 py-1 text-xs font-medium text-stone-600 bg-stone-100 rounded-md border border-stone-200'
    
  when 'gallery'
    badge_size = 'sm'
    badge_component = 'shared/badge_compact'
    container_classes = 'flex justify-center gap-1'
    badge_classes = 'transform transition-transform duration-200 hover:scale-110'
    holographic_intensity = 0.5
    rotation_factor = 10
    glow_intensity = 0.7
    count_classes = 'flex items-center justify-center w-6 h-6 text-xs font-medium text-stone-600 bg-stone-100 rounded-full border border-stone-200'
    
  when 'compact'
    badge_size = 'sm'
    badge_component = 'shared/badge_compact'
    container_classes = 'flex gap-1'
    badge_classes = 'transform transition-transform duration-200 hover:scale-105'
    holographic_intensity = 0.3
    rotation_factor = 6
    glow_intensity = 0.5
    count_classes = 'flex items-center justify-center w-5 h-5 text-xs font-medium text-stone-600 bg-stone-100 rounded-full border border-stone-200'
    
  else
    # Default fallback
    badge_size = 'md'
    badge_component = 'shared/badge'
    container_classes = 'flex flex-wrap gap-2'
    badge_classes = 'transform transition-transform duration-200 hover:scale-105'
    holographic_intensity = 0.5
    rotation_factor = 10
    glow_intensity = 0.8
    count_classes = 'flex items-center justify-center px-2 py-1 text-xs font-medium text-stone-600 bg-stone-100 rounded-md border border-stone-200'
  end
  
  # Combine container classes
  final_container_classes = [container_classes, class_name].compact.join(' ')
%>

<% if displayed_badges.any? %>
  <div class="<%= final_container_classes %>">
    <% displayed_badges.each do |badge_type| %>
      <%= render badge_component, 
          badge_type: badge_type, 
          size: badge_size,
          show_description: true,
          icon_only: icon_only,
          class_name: badge_classes,
          data_attributes: { 
            controller: 'badge',
            badge_target: 'badge',
            badge_holographic_intensity_value: holographic_intensity,
            badge_rotation_factor_value: rotation_factor,
            badge_glow_intensity_value: glow_intensity,
            badge_prismatic_effect_value: true,
            badge_depth_effect_value: (context == 'profile_header' || context == 'profile_section'),
            badge_glitch_effect_value: false
          } %>
    <% end %>
    
    <% if show_count && remaining_count > 0 %>
      <div class="<%= count_classes %>">
        +<%= remaining_count %>
      </div>
    <% end %>
  </div>
<% end %>
