<%# Badge Detail Modal Component %>
<%# This modal displays detailed information about a badge when clicked %>

<div 
  id="badge-modal" 
  class="fixed inset-0 z-50 hidden overflow-y-auto"
  data-controller="badge-modal"
  data-badge-modal-target="container"
  role="dialog"
  aria-modal="true"
  aria-labelledby="badge-modal-title"
  aria-describedby="badge-modal-description"
>
  <!-- Backdrop with blur effect -->
  <div 
    class="fixed inset-0 bg-black/60 backdrop-blur-sm transition-opacity duration-300 ease-out"
    data-badge-modal-target="backdrop"
    data-action="click->badge-modal#close"
    aria-hidden="true"
  ></div>

  <!-- Modal container with view transition support -->
  <div class="flex min-h-full items-center justify-center p-4 sm:p-6 md:p-8">
    <div
      class="relative w-full max-w-sm sm:max-w-md transform overflow-hidden rounded-2xl shadow-2xl transition-all duration-300 ease-out holographic-card-container"
      data-badge-modal-target="content"
      data-action="click->badge-modal#stopPropagation"
      style="view-transition-name: badge-modal-content"
      data-controller="holographic-card"
      data-holographic-card-target="card"
    >
      <!-- Close button -->
      <button
        type="button"
        class="absolute right-4 top-4 z-20 rounded-full bg-black/20 backdrop-blur-sm p-2 text-white/80 hover:bg-black/30 hover:text-white focus:outline-none focus:ring-2 focus:ring-white/50 focus:ring-offset-2 focus:ring-offset-transparent transition-colors duration-200"
        data-action="click->badge-modal#close"
        data-badge-modal-target="closeButton"
        aria-label="Close badge details modal"
        aria-describedby="badge-modal-title"
      >
        <span class="sr-only">Close</span>
        <%= phosphor_icon "x", class: "h-5 w-5", "aria-hidden": "true" %>
      </button>

      <!-- Holographic Card Content -->
      <div class="holographic-card relative rounded-2xl overflow-hidden">
        <!-- Holographic background layers -->
        <div class="absolute inset-0 holographic-bg"></div>
        <div class="absolute inset-0 holographic-gradient"></div>
        <div class="absolute inset-0 holographic-shine"></div>
        <div class="absolute inset-0 holographic-noise"></div>

        <!-- Content container -->
        <div class="relative z-10 p-6 sm:p-8 text-center text-white">
          <!-- Badge display section -->
          <div class="mb-8">
            <!-- Large badge display with enhanced holographic effects -->
            <div
              class="mx-auto mb-6 inline-flex transform-gpu transition-transform duration-500 ease-out hover:scale-105"
              data-badge-modal-target="badgeDisplay"
              data-controller="badge"
              data-badge-target="badge"
              data-badge-holographic-intensity-value="0.8"
              data-badge-rotation-factor-value="15"
              data-badge-glow-intensity-value="1.2"
              data-badge-prismatic-effect-value="true"
              data-badge-depth-effect-value="true"
              style="view-transition-name: badge-hero"
            >
              <!-- Badge content will be dynamically inserted here -->
            </div>

            <!-- Badge title -->
            <h2
              id="badge-modal-title"
              class="text-2xl sm:text-3xl font-bold text-white mb-3 drop-shadow-lg"
              data-badge-modal-target="title"
            >
              <!-- Badge name will be inserted here -->
            </h2>

            <!-- Badge description -->
            <p
              id="badge-modal-description"
              class="text-base sm:text-lg text-white/90 leading-relaxed mb-8 drop-shadow-md"
              data-badge-modal-target="description"
            >
              <!-- Badge description will be inserted here -->
            </p>
          </div>

        </div>

        <!-- Black section at bottom with fine print -->
        <div class="bg-black text-white p-4 sm:p-6">
          <div
            class="text-xs sm:text-sm leading-relaxed opacity-90"
            data-badge-modal-target="criteria"
          >
            This badge represents exceptional achievement and is awarded to recognize outstanding contributions to the platform.
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- CSS for holographic card effects -->
<style>
  /* Holographic Card Container */
  .holographic-card-container {
    perspective: 1000px;
  }

  /* Main Holographic Card */
  .holographic-card {
    position: relative;
    background: linear-gradient(135deg,
      #667eea 0%,
      #764ba2 25%,
      #f093fb 50%,
      #f5576c 75%,
      #4facfe 100%);
    border-radius: 1rem;
    transform-style: preserve-3d;
    transition: transform 0.3s ease-out;
  }

  /* Holographic Background Layers */
  .holographic-bg {
    background: linear-gradient(135deg,
      rgba(102, 126, 234, 0.9) 0%,
      rgba(118, 75, 162, 0.9) 25%,
      rgba(240, 147, 251, 0.9) 50%,
      rgba(245, 87, 108, 0.9) 75%,
      rgba(79, 172, 254, 0.9) 100%);
    border-radius: inherit;
  }

  .holographic-gradient {
    background: linear-gradient(45deg,
      transparent 30%,
      rgba(255, 255, 255, 0.2) 50%,
      transparent 70%);
    border-radius: inherit;
    opacity: 0.8;
    mix-blend-mode: overlay;
  }

  .holographic-shine {
    background: linear-gradient(135deg,
      rgba(255, 255, 255, 0.3) 0%,
      transparent 20%,
      transparent 80%,
      rgba(255, 255, 255, 0.3) 100%);
    border-radius: inherit;
    opacity: 0.6;
    animation: shine 3s ease-in-out infinite;
  }

  .holographic-noise {
    background-image:
      radial-gradient(circle at 20% 50%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),
      radial-gradient(circle at 80% 20%, rgba(255, 119, 198, 0.3) 0%, transparent 50%),
      radial-gradient(circle at 40% 80%, rgba(120, 219, 255, 0.3) 0%, transparent 50%);
    border-radius: inherit;
    opacity: 0.4;
    animation: float 6s ease-in-out infinite;
  }

  /* Animations */
  @keyframes shine {
    0%, 100% {
      transform: translateX(-100%) rotate(45deg);
    }
    50% {
      transform: translateX(100%) rotate(45deg);
    }
  }

  @keyframes float {
    0%, 100% {
      transform: translateY(0px) rotate(0deg);
    }
    33% {
      transform: translateY(-10px) rotate(1deg);
    }
    66% {
      transform: translateY(5px) rotate(-1deg);
    }
  }

  /* Hover Effects */
  .holographic-card-container:hover .holographic-card {
    transform: rotateY(5deg) rotateX(5deg);
  }

  /* Modal entrance animation */
  @keyframes modalEnter {
    from {
      opacity: 0;
      transform: scale(0.9) translateY(20px);
    }
    to {
      opacity: 1;
      transform: scale(1) translateY(0);
    }
  }

  .modal-enter {
    animation: modalEnter 0.4s ease-out forwards;
  }

  /* Enhanced backdrop blur */
  .backdrop-blur-sm {
    backdrop-filter: blur(12px);
    -webkit-backdrop-filter: blur(12px);
  }

  /* View transition support */
  @supports (view-transition-name: none) {
    #badge-modal {
      view-transition-name: badge-modal;
    }
  }

  /* Accessibility: Respect reduced motion preferences */
  @media (prefers-reduced-motion: reduce) {
    .holographic-shine,
    .holographic-noise {
      animation: none !important;
    }

    .holographic-card {
      transition: none !important;
    }

    #badge-modal * {
      animation-duration: 0.01ms !important;
      transition-duration: 0.01ms !important;
    }
  }

  /* High contrast mode support */
  @media (prefers-contrast: high) {
    .holographic-card {
      background: #1a1a1a !important;
      border: 2px solid white;
    }

    .holographic-bg,
    .holographic-gradient,
    .holographic-shine,
    .holographic-noise {
      display: none;
    }
  }

  /* Mobile responsive styles */
  @media (max-width: 640px) {
    .holographic-card-container {
      max-width: 90vw;
    }

    .holographic-card {
      border-radius: 1rem;
    }
  }

  /* Touch device optimizations */
  @media (hover: none) and (pointer: coarse) {
    .holographic-card-container:hover .holographic-card {
      transform: none;
    }
  }
</style>
