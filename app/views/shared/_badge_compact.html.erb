<%#
  Badge Component - Compact Version for Search Results

  Usage:
  render 'shared/badge_compact', badge_type: badge_type
  render 'shared/badge_compact', badge_type: badge_type, show_description: true
  render 'shared/badge_compact', badge_type: badge_type, class_name: 'ml-2'

  Parameters:
  - badge_type: BadgeType object (required)
  - show_description: boolean (default: true) - shows description on hover
  - class_name: additional CSS classes
  - data_attributes: hash of additional data attributes
  - icon_only: boolean (default: false) - shows only icon for very compact display
%>

<%
  # Set default values
  badge_type = local_assigns.fetch(:badge_type)
  show_description = local_assigns.fetch(:show_description, true)
  class_name = local_assigns.fetch(:class_name, '')
  data_attributes = local_assigns.fetch(:data_attributes, {})
  icon_only = local_assigns.fetch(:icon_only, false)
  
  # Validate badge_type
  unless badge_type.is_a?(BadgeType)
    raise ArgumentError, "badge_type must be a BadgeType object"
  end
  
  # Compact styling - smaller padding and text
  size_classes = if icon_only
    'px-1.5 py-1 text-xs'
  else
    'px-2 py-0.5 text-xs'
  end
  
  # Icon size for compact display
  icon_size_classes = if icon_only
    'text-xs'
  else
    'text-xs mr-1'
  end
  
  # Build CSS styles from badge_type
  badge_styles = [
    "background-color: #{badge_type.background_color}",
    "color: #{badge_type.text_color}",
    "border-color: #{badge_type.background_color}"
  ].join('; ')
  
  # Combine all CSS classes for compact badge with holographic effects
  badge_classes = [
    'badge-compact',
    'inline-flex',
    'items-center',
    'font-medium',
    'rounded-md',
    'border',
    'shadow-sm',
    'backdrop-filter',
    'backdrop-blur-sm',
    'bg-opacity-90',
    'transition-all',
    'duration-200',
    'ease-out',
    'transform-gpu',
    'will-change-transform',
    'cursor-default',
    'prismatic',
    size_classes,
    class_name
  ].compact.join(' ')
  
  # Remove tooltip content - we'll use modal instead
  # tooltip_content = show_description ? badge_type.description : nil
%>

<div
  class="<%= badge_classes %> cursor-pointer"
  style="<%= badge_styles %>"
  data-controller="badge-click"
  data-badge-click-badge-id-value="<%= badge_type.id %>"
  data-badge-click-badge-name-value="<%= html_escape(badge_type.name) %>"
  data-badge-click-badge-description-value="<%= html_escape(badge_type.description) %>"
  data-badge-click-badge-icon-value="<%= badge_type.icon %>"
  data-badge-click-badge-background-color-value="<%= badge_type.background_color %>"
  data-badge-click-badge-text-color-value="<%= badge_type.text_color %>"
  data-badge-click-badge-criteria-value="This badge is awarded for <%= badge_type.name.downcase %> and represents exceptional achievement on the platform."
  data-action="click->badge-click#openModal keydown->badge-click#handleKeydown"
  role="button"
  tabindex="0"
  aria-label="<%= html_escape("#{badge_type.name} badge - click for details") %>"
>
  <%# Badge Icon %>
  <% if badge_type.icon.present? %>
    <%= phosphor_icon badge_type.icon, class: "badge-icon #{icon_size_classes}", "aria-hidden": "true" %>
  <% end %>
  
  <%# Badge Name (hidden in icon-only mode) %>
  <% unless icon_only %>
    <span class="badge-name font-medium">
      <%= badge_type.name %>
    </span>
  <% end %>
  
  <%# Tooltips removed - using modal instead %>
</div>

<%# Enhanced CSS for compact badge effects %>
<style>
  .badge-compact {
    position: relative;
    overflow: hidden;
    max-width: 120px; /* Prevent badges from getting too wide in search results */
  }
  
  .badge-compact::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0.05) 100%);
    pointer-events: none;
    border-radius: inherit;
  }
  
  .badge-compact:hover .badge-tooltip-compact {
    visibility: visible;
    opacity: 1;
  }
  
  /* Compact badge specific hover effects */
  .badge-compact:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  }
  
  /* Ensure text doesn't overflow in compact mode */
  .badge-compact .badge-name {
    max-width: 80px;
  }
  
  /* Icon-only mode adjustments */
  .badge-compact[data-icon-only="true"] {
    max-width: 24px;
    justify-content: center;
  }
  
  /* Ensure smooth transitions for compact badge elements */
  .badge-compact .badge-icon {
    transition: transform 0.2s ease-out;
  }
  
  /* Accessibility: Respect reduced motion preferences */
  @media (prefers-reduced-motion: reduce) {
    .badge-compact {
      transition: none !important;
    }
    
    .badge-compact .badge-icon {
      transition: none !important;
    }
    
    .badge-compact:hover {
      transform: none !important;
    }
  }
  
  /* High contrast mode support */
  @media (prefers-contrast: high) {
    .badge-compact {
      border-width: 2px;
      backdrop-filter: none;
      background-opacity: 1 !important;
    }
  }
  
  /* Mobile responsiveness */
  @media (max-width: 640px) {
    .badge-compact {
      max-width: 100px;
    }
    
    .badge-compact .badge-name {
      max-width: 60px;
    }
  }
  
  /* Print styles */
  @media print {
    .badge-compact {
      background: white !important;
      color: black !important;
      border: 1px solid black !important;
      box-shadow: none !important;
      transform: none !important;
    }
    
    .badge-tooltip-compact {
      display: none !important;
    }
  }
</style>
