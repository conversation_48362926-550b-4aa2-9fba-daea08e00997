<div data-controller="scout-sidebar" class="flex flex-col min-h-screen transition-opacity duration-100 ease-linear opacity-0 lg:flex-row">
  <!-- Sidebar -->
  <div class="flex flex-col w-1/5 lg:min-h-screen">
    <div class="flex px-5 py-4 mb-2 ">
      <span class="text-3xl font-semibold tracking-[-0.4px]">Find Talent</span>
      <span class="text-3xl ml-1 font-semibold text-[#6100FF] tracking-[-0.4px]">.</span>
    </div>

      <!-- Search -->
      <div class="px-4 mb-4">
        <%= form_with url: scout_talent_index_path, method: :get, data: { 
              controller: "talent-search", 
              talent_search_target: "form",
              turbo_frame: "talent-list"
            } do |f| %>
          <%= f.search_field :query,
                value: params[:query],
                placeholder: "Search by name ...",
                class: "w-full bg-white border border-stone-900 rounded-md px-4 text-sm",
                data: { 
                  talent_search_target: "input",
                  action: "input->talent-search#search"
                } %>
        <% end %>
      </div>
    <div class="px-5 py-2 text-xs text-stone-700">
      <ul class="text-xs list-disc list-inside text-stone-600">
        <li class="py-1">View detailed talent profiles.</li>
<li class="py-1">Filter talent by skills and pricing.</li>

        <li class="py-1">Search talent by name or keywords.</li>

        <li class="py-1">Invite top talent to apply for your jobs.</li>
        <li class="py-1">Bookmark promising candidates for later.</li>
      </ul>
    </div>

  </div>
  
  <!-- Main Content -->
  <div class="flex w-4/5 p-4 m-2 bg-white border rounded-md min-h-screen">
    <main data-scout-sidebar-target="main" class="w-full p-4" data-controller="view-toggle">

      <!-- Filters Section -->
      <div class="mb-6">
        <h3 class="mb-4 text-sm font-semibold text-stone-900">Talent Filters</h3>
        <%= render partial: "scout/talent/filters_content" %>

        <!-- Active Filter Tags -->
        <div data-controller="talent-filter-tags" class="mt-4">
          <%= render partial: "scout/talent/active_filter_tags" %>
        </div>
      </div>

      <!-- Results header -->
      <div class="flex items-center justify-between mb-6">
        <div class="text-sm font-semibold text-[#1f2937]"><%= pluralize @total_count, "Writer" %> Found</div>
        <div class="flex items-center gap-2">
          <%# View Toggle Buttons %>
          <button type="button"
                  title="List View"
                  data-action="click->view-toggle#showList"
                  data-view-toggle-target="toggleButtonList"
                  class="p-1.5 rounded-md border border-stone-200 hover:bg-stone-50 text-stone-500">
            <%= phosphor_icon "list", style: :bold, class: "h-5 w-5" %>
          </button>
          <button type="button"
                  title="Gallery View"
                  data-action="click->view-toggle#showGallery"
                  data-view-toggle-target="toggleButtonGallery"
                  class="p-1.5 rounded-md border border-stone-200 hover:bg-stone-50 text-stone-500">
            <%= phosphor_icon "grid-four", style: :bold, class: "h-5 w-5" %>
          </button>

          <button data-action="click->scout-sidebar#toggle" data-sidebar-target="toggleButton" class="w-[34px] h-[34px] border border-[#e5e7eb] rounded flex items-center justify-center cursor-pointer">
            <%= phosphor_icon "sidebar-simple", style: "duotone" , class: "
            rotate-180 h-5 w-5" %>
          </button>
        </div>
      </div>

      <!-- All Writers Section -->
      <div id="talent-list-container" data-view-toggle-target="listContainer">
        <%= render partial: "scout/talent/talent_profiles", locals: { talent_profiles: @talent_profiles, view_type: :list } %>
      </div>
      <div id="talent-gallery-container" data-view-toggle-target="galleryContainer" class="hidden">
        <%= render partial: "scout/talent/talent_profiles", locals: { talent_profiles: @talent_profiles, view_type: :gallery } %>
      </div>

    </main>
    <aside data-scout-sidebar-target="aside" data-controller="sidebar-stats" class="hidden w-4/12 p-4 mx-auto ml-2 overflow-hidden border-l candidate-details-container border-stone-100">
      <div data-controller="candidate-details">
        <%= turbo_frame_tag "candidate-details-container" do %>
          <%= render "placeholder" %>
        <% end %>
      </div>
    </aside>
  </div>
</div>

<!-- Chat Request Modal -->
<%= render "shared/modal" %>

<!-- Badge Modal -->
<%= render "shared/badge_modal" %>
